// 测试字幕监听器功能的脚本
// 在浏览器控制台中运行此脚本来测试功能

console.log('🧪 开始测试字幕监听器功能...');

// 测试函数
function testSubtitleListener() {
  if (typeof window.SubtitleListener === 'undefined') {
    console.error('❌ SubtitleListener 未找到，请确保字幕监听器已正确注入');
    return;
  }

  console.log('✅ SubtitleListener 已找到');
  console.log('📋 可用的API方法:', Object.keys(window.SubtitleListener));

  // 测试获取已捕获的URL
  const capturedUrls = window.SubtitleListener.getCapturedUrls();
  console.log('📝 已捕获的字幕URL:', capturedUrls);

  // 测试获取双语URL
  const bilingualUrls = window.SubtitleListener.getBilingualUrls();
  console.log('🌐 生成的双语URL:', bilingualUrls);

  // 如果有捕获的URL但没有双语URL，尝试验证并生成
  if (capturedUrls.length > 0 && Object.keys(bilingualUrls).length === 0) {
    console.log('🔄 尝试验证并生成双语URL...');
    window.SubtitleListener.validateAndGenerateBilingualUrls(capturedUrls[0]);
  }

  // 测试URL验证功能
  if (capturedUrls.length > 0) {
    console.log('🔍 测试URL验证功能...');
    window.SubtitleListener.validateSubtitleUrl(capturedUrls[0]).then(isValid => {
      console.log('URL验证结果:', isValid ? '✅ 有效' : '❌ 无效');
    });
  }

  // 测试点击字幕按钮
  console.log('🔘 测试点击字幕按钮...');
  window.SubtitleListener.clickSubtitleButton();
}

// 测试轨道信息获取
function testTrackInfo() {
  console.log('🎬 测试轨道信息获取...');

  try {
    const playerResponse = window.ytInitialPlayerResponse;
    if (!playerResponse) {
      console.warn('⚠️ ytInitialPlayerResponse 未找到');
      return;
    }

    const captionTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks || [];
    const audioTracks = playerResponse?.captions?.playerCaptionsTracklistRenderer?.audioTracks || [];

    console.log(`📋 找到 ${captionTracks.length} 个字幕轨道`);

    // 分析轨道类型和语言
    const userTracks = captionTracks.filter(track => !track.kind);
    const asrTracks = captionTracks.filter(track => track.kind === 'asr');
    const translatableTracks = captionTracks.filter(track => track.isTranslatable);

    console.log('👤 用户上传的轨道数量:', userTracks.length);
    console.log('🤖 自动生成的轨道数量:', asrTracks.length);
    console.log('🌐 可翻译的轨道数量:', translatableTracks.length);

    // 显示语言分布
    const languages = [...new Set(captionTracks.map(track => track.languageCode))];
    console.log('🌍 可用语言:', languages);

    // 测试语言变体匹配
    const englishTracks = captionTracks.filter(track =>
      track.languageCode.startsWith('en'));
    const chineseTracks = captionTracks.filter(track =>
      track.languageCode.startsWith('zh'));
    const japaneseTracks = captionTracks.filter(track =>
      track.languageCode.startsWith('ja'));

    if (englishTracks.length > 0) {
      console.log('🇺🇸 英语轨道:', englishTracks.map(t => t.languageCode));
    }
    if (chineseTracks.length > 0) {
      console.log('🇨🇳 中文轨道:', chineseTracks.map(t => t.languageCode));
    }
    if (japaneseTracks.length > 0) {
      console.log('🇯🇵 日语轨道:', japaneseTracks.map(t => t.languageCode));
    }

  } catch (error) {
    console.error('❌ 获取轨道信息时出错:', error);
  }
}

// 运行测试
testSubtitleListener();
testTrackInfo();

console.log('🎯 测试完成！请查看上面的输出结果。');
console.log('💡 提示：如果没有看到字幕URL，请播放一个有字幕的YouTube视频并等待几秒钟。');
