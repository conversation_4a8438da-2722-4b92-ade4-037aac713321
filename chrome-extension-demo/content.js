// YouTube页面内容脚本
console.log('YouTube谷歌登录插件已在当前页面加载');

// 检查是否在YouTube网站上
if (window.location.hostname.includes('youtube.com')) {
  // 防止重复注入
  if (!document.getElementById('youtube-login-sidebar')) {
    // 注入侧边栏
    injectSidebar();
  }
  
  // 向背景脚本请求用户信息
  chrome.runtime.sendMessage({action: 'getUserInfo'}, function(response) {
    if (response && response.userInfo) {
      // 用户已登录，可以在页面上显示用户信息
      console.log('用户已登录:', response.userInfo.name);
    } else {
      // 用户未登录
      console.log('用户未登录');
    }
  });

  // 注入PlayerResponse Hook脚本
  injectHookScript();

  // 注入字幕监听器脚本
  injectSubtitleListenerScript();
}

// 注入侧边栏到页面
function injectSidebar() {
  // 创建iframe元素
  const sidebarFrame = document.createElement('iframe');
  sidebarFrame.id = 'youtube-login-sidebar';
  sidebarFrame.src = chrome.runtime.getURL('components/sidebar/sidebar.html');
  sidebarFrame.style.position = 'fixed';
  sidebarFrame.style.top = '0';
  sidebarFrame.style.left = '0';
  sidebarFrame.style.width = '320px'; // 固定宽度，避免布局重排
  sidebarFrame.style.height = '100%';
  sidebarFrame.style.border = 'none';
  sidebarFrame.style.zIndex = '10002'; // 确保高于播放器的z-index (10001)
  sidebarFrame.style.transform = 'translateX(-100%)'; // 初始隐藏在左侧
  sidebarFrame.style.transition = 'transform 0.3s ease-in-out'; // 使用transform动画
  sidebarFrame.style.willChange = 'transform'; // 性能优化
  
  // 添加到页面
  document.body.appendChild(sidebarFrame);
  
  // 创建一个浮动按钮，用于打开侧边栏
  const toggleButton = document.createElement('div');
  toggleButton.id = 'sidebar-toggle-button';
  toggleButton.innerHTML = `
    <i class="fas fa-chevron-right text-lg"></i>
  `;
  toggleButton.style.position = 'fixed';
  toggleButton.style.top = '50%';
  toggleButton.style.left = '0';
  toggleButton.style.transform = 'translateY(-50%) translateX(0)'; // 初始位置
  toggleButton.style.backgroundColor = '#4b5563';
  toggleButton.style.color = 'white';
  toggleButton.style.width = '2.5rem';
  toggleButton.style.height = '5rem';
  toggleButton.style.borderRadius = '0 0.5rem 0.5rem 0';
  toggleButton.style.display = 'flex';
  toggleButton.style.alignItems = 'center';
  toggleButton.style.justifyContent = 'center';
  toggleButton.style.cursor = 'pointer';
  toggleButton.style.zIndex = '2147483646';
  toggleButton.style.transition = 'transform 0.3s ease'; // 只对transform做动画
  toggleButton.style.willChange = 'transform'; // 性能优化
  
  // 添加Font Awesome字体图标
  if (!document.querySelector('link[href*="font-awesome"]')) {
    const fontAwesome = document.createElement('link');
    fontAwesome.rel = 'stylesheet';
    fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css';
    fontAwesome.integrity = 'sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==';
    fontAwesome.crossOrigin = 'anonymous';
    fontAwesome.referrerPolicy = 'no-referrer';
    document.head.appendChild(fontAwesome);
  }
  
  // 添加自定义样式
  const customStyle = document.createElement('style');
  customStyle.textContent = `
    .text-lg {
      font-size: 1.125rem; /* 18px */
      line-height: 1.75rem; /* 28px */
    }
  `;
  document.head.appendChild(customStyle);
  
  // 添加到页面
  document.body.appendChild(toggleButton);
  
  // 侧边栏状态
  let sidebarOpen = false;
  
  // 切换侧边栏
  toggleButton.addEventListener('click', function() {
    sidebarOpen = !sidebarOpen;
    if (sidebarOpen) {
      sidebarFrame.style.transform = 'translateX(0)'; // 显示侧边栏
      toggleButton.style.transform = 'translateY(-50%) translateX(320px)'; // 按钮移动到侧边栏右侧
      toggleButton.querySelector('i').className = 'fas fa-chevron-left text-lg';
    } else {
      sidebarFrame.style.transform = 'translateX(-100%)'; // 隐藏侧边栏
      toggleButton.style.transform = 'translateY(-50%) translateX(0)'; // 按钮回到左侧
      toggleButton.querySelector('i').className = 'fas fa-chevron-right text-lg';
    }
  });
  
  // 监听来自iframe的消息
  window.addEventListener('message', function(event) {
    // 确保消息来自我们的扩展
    if (event.origin === chrome.runtime.getURL('').slice(0, -1)) {
      if (event.data.action === 'toggleSidebar') {
        // 切换侧边栏
        toggleButton.click();
      } else if (event.data.action === 'userLoggedIn') {
        // 用户登录成功
        console.log('用户已登录:', event.data.userInfo.name);
      }
    }
  });
  
  // 监听来自background.js的消息
  chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
    console.log('Content script收到消息:', message);
    
    // 处理切换学习模式的消息
    if (message.action === 'toggleStudyMode') {
        toggleStudyMode();
        sendResponse({success: true});
        return true;
    }
    
    // 处理获取视频信息的消息
    if (message.action === 'getVideoInfo') {
        const videoInfo = getVideoInfo();
        console.log('获取到视频信息:', videoInfo);
        sendResponse(videoInfo);
        return true;
    }

    // 处理ping消息(用于检测content script是否已注入)
    if (message.action === 'ping') {
        sendResponse({success: true, message: 'Content script已加载'});
        return true;
    }

    // 处理视频语言切换消息
    if (message.action === 'videoLanguageChanged') {
        console.log('Content script收到语言切换消息:', message.language);

        // 如果学习模式处于激活状态，通知学习模式更新字幕
        if (window.StudyModeCore && window.StudyModeCore.getStudyModeActive()) {
            console.log('学习模式激活中，通知更新字幕');

            // 通过postMessage发送消息到学习模式
            window.postMessage({
                type: 'VIDEO_LANGUAGE_CHANGED',
                language: message.language
            }, '*');
        }

        sendResponse({success: true});
        return true;
    }

    // 默认响应
    sendResponse({success: false, message: '未知消息类型'});
    return true;
  });
}

// 获取YouTube视频信息
function getVideoInfo() {
  try {
    // 从URL中提取视频ID
    const videoId = extractVideoIdFromUrl(window.location.href);

    // 从页面DOM中获取视频标题
    const videoTitle = getVideoTitle();

    if (videoId && videoTitle) {
      return {
        videoId: videoId,
        videoTitle: videoTitle
      };
    } else {
      console.warn('无法获取完整的视频信息', { videoId, videoTitle });
      return null;
    }
  } catch (error) {
    console.error('获取视频信息时出错:', error);
    return null;
  }
}

// 从URL中提取视频ID
function extractVideoIdFromUrl(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// 获取视频标题
function getVideoTitle() {
  // 尝试多种选择器来获取视频标题
  const titleSelectors = [
    'yt-formatted-string.ytd-watch-metadata h1',
    'h1.ytd-watch-metadata yt-formatted-string',
    'h1.title.ytd-video-primary-info-renderer',
    'h1.ytd-video-primary-info-renderer',
    '.ytd-video-primary-info-renderer h1'
  ];

  for (const selector of titleSelectors) {
    const titleElement = document.querySelector(selector);
    if (titleElement && titleElement.textContent.trim()) {
      return titleElement.textContent.trim();
    }
  }

  // 如果都没找到，尝试从页面标题获取
  const pageTitle = document.title;
  if (pageTitle && pageTitle !== 'YouTube') {
    // 移除 " - YouTube" 后缀
    return pageTitle.replace(' - YouTube', '').trim();
  }

  return null;
}

// 向iframe发送消息的辅助函数
function sendMessageToSidebar(message) {
  const sidebarFrame = document.getElementById('youtube-login-sidebar');
  if (sidebarFrame) {
    sidebarFrame.contentWindow.postMessage(message, chrome.runtime.getURL('components/sidebar/sidebar.html'));
  }
}

// 注入用于捕获playerResponse的hook脚本
function injectHookScript() {
  const script = document.createElement('script');
  script.src = chrome.runtime.getURL('modules/player-response-hook.js');
  (document.head || document.documentElement).appendChild(script);
  script.onload = function() {
    this.remove();
  };
  console.log('PlayerResponse hook script injected.');
}

// 注入字幕监听器脚本
function injectSubtitleListenerScript() {
  const script = document.createElement('script');
  script.src = chrome.runtime.getURL('modules/subtitle-listener.js');
  (document.head || document.documentElement).appendChild(script);
  script.onload = function() {
    this.remove();
  };
  console.log('Subtitle listener script injected.');
}