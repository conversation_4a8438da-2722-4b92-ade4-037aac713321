// Study Mode Core Module - 核心控制模块
console.log('Study Mode Core module loaded');

// 学习模式状态
let studyModeActive = false;
let subtitleManager = null;

// 保存原始元素信息
let originalState = {
  parent: null,        // 原始父元素
  nextSibling: null,   // 原始下一个兄弟元素
  styles: {},          // 原始样式
  bodyOverflow: '',     // 原始body滚动状态
  htmlOverflow: ''     // 原始html滚动状态
};

// 防止重复切换的标志
let isTogglingStudyMode = false;

// 全局变量存储视频切换监听器
let currentVideoId = null;
let studyModeUrlChangeHandler = null;
let studyModeResizeHandler = null;
let playerDoubleClickHandler = null;

/**
 * 学习模式主要控制函数
 */

// 进入学习模式函数 - 供面板按钮调用
async function enterStudyMode() {
  // 检查学习模式的真实状态
  const overlay = document.getElementById('study-mode-overlay');
  const isActuallyInStudyMode = overlay && overlay.style.display === 'block';
  
  // 如果实际上已经在学习模式中，则同步状态并返回
  if (isActuallyInStudyMode) {
    console.log('实际上已在学习模式中，同步状态');
    studyModeActive = true;
    return;
  }
  
  // 如果实际上不在学习模式中，但变量显示在学习模式中，则同步状态
  if (!isActuallyInStudyMode && studyModeActive) {
    console.log('状态不同步，更正studyModeActive为false');
    studyModeActive = false;
  }
  
  // 如果正在切换中，则跳过
  if (isTogglingStudyMode) {
    console.log('正在处理学习模式切换，请稍候...');
    return;
  }
  
  isTogglingStudyMode = true;
  
  try {
    if (!window.location.hostname.includes('youtube.com')) {
      console.log('Study mode only works on YouTube');
      return;
    }

    // Get the video player element
    const videoElement = document.querySelector('video.html5-main-video');
    if (!videoElement) {
      console.log('Video element not found');
      return;
    }
    
    // 验证会话状态
    const isSessionValid = await window.StudyModeSession.validateSession();
    if (!isSessionValid) {
      console.log('会话无效，无法进入学习模式');
      window.StudyModeUtils.showNotification('请先登录再使用学习模式', 'error');
      return;
    }

    // 启用学习模式
    enableStudyMode(videoElement);
    studyModeActive = true;
    
    // 设置全局状态，供其他模块访问
    window.studyModeActive = true;
    window.subtitleManager = subtitleManager;
    
    console.log('已成功进入学习模式');
  } catch (error) {
    console.error('进入学习模式时出错:', error);
  } finally {
    isTogglingStudyMode = false;
  }
}

// 退出学习模式函数 - 供退出按钮调用
async function exitStudyMode() {
  console.log('退出学习模式');
  
  // 执行UI和模块清理
  disableStudyMode();

  try {
    // 清除聊天会话（后端数据）
    await window.StudyModeSession.clearChatSession();
    
    // 清除当前视频的本地聊天会话ID
    const currentVideoId = window.StudyModeUtils.extractVideoId(window.location.href);
    if (currentVideoId) {
      const storageKey = `youtube_chat_session_${currentVideoId}`;
      sessionStorage.removeItem(storageKey);
      console.log(`已清除视频 ${currentVideoId} 的本地会话ID`);
    }
    
    // 显示退出通知 - disableStudyMode 已经处理了通用通知
    // window.StudyModeUtils.showEnhancedNotification('学习模式', '已退出学习模式，聊天数据已清除');
    
  } catch (error) {
    console.error('退出学习模式时发生错误:', error);
    // 即使清理失败，也要继续退出流程
    window.StudyModeUtils.showEnhancedNotification('学习模式', '已退出学习模式（部分数据清理可能失败）');
  }
}

// 切换学习模式函数 - 内部使用
async function toggleStudyMode() {
  // 检查学习模式的真实状态
  const overlay = document.getElementById('study-mode-overlay');
  const isActuallyInStudyMode = overlay && overlay.style.display === 'block';
  
  // 确保状态变量与实际状态一致
  if (studyModeActive !== isActuallyInStudyMode) {
    console.log('状态不一致，正在同步:', studyModeActive, '->', isActuallyInStudyMode);
    studyModeActive = isActuallyInStudyMode;
  }
  
  // 根据实际状态决定操作
  if (isActuallyInStudyMode) {
    await exitStudyMode();
  } else {
    await enterStudyMode();
  }
}

/**
 * 学习模式启用和禁用
 */

// Function to enable study mode
function enableStudyMode(videoElement) {
  console.log('Enabling study mode');
  
  // 清除权限缓存，确保获取最新的用户权限状态
  if (window.SubtitleCore && window.SubtitleCore.clearPermissionCache) {
    window.SubtitleCore.clearPermissionCache();
    console.log('权限缓存已清除，将重新检查用户权限');
  }
  
  // 加载学习模式的CSS样式
  window.StudyModeUtils.loadStudyModeStyles();
  
  // 加载弹幕模块样式
  window.StudyModeUtils.loadDanmakuStyles();
  
  // 保存当前视频播放进度
  const currentTime = videoElement.currentTime;
  const isPlaying = !videoElement.paused;
  
  // 获取YouTube播放器元素
  const playerContainer = document.getElementById('movie_player') || 
                         document.querySelector('.html5-video-player');
  
  if (!playerContainer) {
    console.log('Player container not found');
    return;
  }

  console.log('Player container found:', playerContainer);
  
  // 保存原始状态
  originalState = {
    parent: playerContainer.parentElement,
    nextSibling: playerContainer.nextSibling,
    styles: {},
    bodyOverflow: document.body.style.overflow,
    htmlOverflow: document.documentElement.style.overflow
  };
  
  // 保存所有计算样式
  const computedStyle = window.getComputedStyle(playerContainer);
  const stylesToSave = [
    'position', 'zIndex', 'top', 'left', 'right', 'bottom',
    'width', 'height', 'transform', 'margin', 'maxWidth',
    'display', 'visibility', 'opacity', 'transition'
  ];
  
  stylesToSave.forEach(prop => {
    originalState.styles[prop] = computedStyle[prop];
  });
  
  // 禁用页面滚动
  document.body.style.overflow = 'hidden';
  
  // 创建或显示学习模式覆盖层
  let overlay = document.getElementById('study-mode-overlay');
  if (!overlay) {
    overlay = document.createElement('div');
    overlay.id = 'study-mode-overlay';
    document.body.appendChild(overlay);
  }
  overlay.style.display = 'block';
  
  // 为覆盖层添加动画类
  overlay.classList.add('entering');
  setTimeout(() => {
    overlay.classList.remove('entering');
  }, 50);

  console.log('Original styles saved:', originalState.styles);

  // 创建播放器容器
  let playerContainerDiv = document.getElementById('study-mode-player-container');
  if (!playerContainerDiv) {
    playerContainerDiv = document.createElement('div');
    playerContainerDiv.id = 'study-mode-player-container';
    playerContainerDiv.className = 'study-mode-player-container';
    overlay.appendChild(playerContainerDiv);
  } else {
    playerContainerDiv.style.display = 'block';
  }
  
  // 创建播放器包装器
  let playerWrapper = document.getElementById('study-mode-player-wrapper');
  if (!playerWrapper) {
    playerWrapper = document.createElement('div');
    playerWrapper.id = 'study-mode-player-wrapper';
    playerWrapper.className = 'study-mode-player-wrapper';
    playerContainerDiv.appendChild(playerWrapper);
  }
  
  // 移动播放器到学习模式容器
  playerWrapper.appendChild(playerContainer);
  
  // 应用样式使播放器正确显示在容器内
  playerContainer.style.position = 'absolute';
  playerContainer.style.zIndex = '1';
  playerContainer.style.top = '0';
  playerContainer.style.left = '0';
  playerContainer.style.width = '100%';
  playerContainer.style.height = '100%';
  playerContainer.style.maxWidth = 'none';
  playerContainer.style.margin = '0';
  playerContainer.style.transform = 'none';
  
  // 添加学习模式标记类
  playerContainer.classList.add('in-study-mode');
  
  // 添加淡入动画标记类
  playerContainer.classList.add('entering');
  playerContainerDiv.classList.add('entering');
  
  // 延迟50ms后移除entering类，触发过渡动画
  setTimeout(() => {
    playerContainer.classList.remove('entering');
    playerContainerDiv.classList.remove('entering');
  }, 50);

  // 创建聊天区域
  window.StudyModeChat.createChatArea(overlay);

  // 创建进度条和控制按钮区域
  window.StudyModeControls.createProgressAndControlsArea(overlay);

  // Create exit button if it doesn't exist
  let exitButton = document.getElementById('exit-study-mode');
  if (!exitButton) {
    exitButton = document.createElement('button');
    exitButton.id = 'exit-study-mode';
    exitButton.innerHTML = '<i class="fas fa-times"></i> 退出学习模式';
    exitButton.addEventListener('click', disableStudyMode);
    document.body.appendChild(exitButton);
  }
  
  // Show exit button with animation
  exitButton.style.display = 'block';
  exitButton.classList.add('entering');
  setTimeout(() => {
    exitButton.classList.remove('entering');
  }, 50);

  // 计算合适的尺寸，确保不会超出屏幕
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  // 为播放器预留的最大宽度（考虑左右边距）
  const maxWidth = Math.min(viewportWidth * 0.9, 1280);
  const aspectRatio = 16 / 9;
  const calculatedHeight = maxWidth / aspectRatio;
  
  // 计算可用高度（考虑顶部偏移和底部边距）
  const availableHeight = viewportHeight - 20 - 40;
  const maxHeight = Math.min(availableHeight, calculatedHeight);
  
  let finalWidth, finalHeight;
  if (calculatedHeight <= maxHeight) {
    finalWidth = maxWidth;
    finalHeight = calculatedHeight;
  } else {
    finalHeight = maxHeight;
    finalWidth = maxHeight * aspectRatio;
  }
  
  // 确保最终宽度不超过可用空间
  if (finalWidth > viewportWidth * 0.9) {
    finalWidth = viewportWidth * 0.9;
    finalHeight = finalWidth / aspectRatio;
  }
  
  playerContainer.style.width = finalWidth + 'px';
  playerContainer.style.height = finalHeight + 'px';
  
  console.log('Player container styled:', {
    width: finalWidth,
    height: finalHeight,
    position: playerContainer.style.position,
    zIndex: playerContainer.style.zIndex,
    top: playerContainer.style.top,
    transform: playerContainer.style.transform,
    viewportWidth: viewportWidth,
    viewportHeight: viewportHeight
  });
  
  // Prevent scrolling
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';
  
  // 延迟触发resize事件
  setTimeout(() => {
    window.dispatchEvent(new Event('resize'));
    console.log('Resize event dispatched');
    
    // 调整聊天区域高度
    window.StudyModeChat.adjustChatContainerHeight();
  }, 100);

  // 添加窗口resize监听器，确保聊天区域始终与播放器对齐
  studyModeResizeHandler = () => {
    setTimeout(() => {
      window.StudyModeChat.adjustChatContainerHeight();
    }, 50);
  };
  window.addEventListener('resize', studyModeResizeHandler);

  // 初始化字幕管理器
  initializeSubtitleManager(playerContainerDiv);
  
  // 初始化弹幕系统
  initializeDanmakuSystem(playerContainerDiv);
  
  // 初始化缩略图预览功能
  initializeStoryboardPreview();
  
  // 设置video状态
  if (isPlaying) {
    videoElement.play().catch(e => console.log('自动播放失败:', e));
  }
  videoElement.currentTime = currentTime;
  
  // 监听滚动事件，确保学习模式中滚动正常工作
  window.StudyModeUtils.ensureScrollingWorks();
  
  // 隐藏页面上其他内容
  window.StudyModeUtils.hidePageContent(playerContainer);
  
  // 设置视频播放监听
  window.StudyModeSession.setupVideoPlaybackMonitoring();
  
  // 设置视频切换监听
  setupVideoChangeListener();

  // 设置双击播放器切换全屏
  setupPlayerDoubleClickFullscreen(playerContainer);

  // 确保视频播放器获得焦点
  window.StudyModeUtils.focusVideoPlayer();
}

// Function to disable study mode
function disableStudyMode() {
  console.log('Disabling study mode');
  
  // 获取学习模式中的播放器
  const playerContainer = document.querySelector('.in-study-mode');
  if (!playerContainer) {
    console.log('No player found in study mode');
    return;
  }
  
  // 保存当前视频状态
  const videoElement = playerContainer.querySelector('video');
  let currentTime = 0;
  let isPlaying = false;
  
  if (videoElement) {
    currentTime = videoElement.currentTime;
    isPlaying = !videoElement.paused;
    // 暂停视频，避免在移动回原始位置时继续播放
    videoElement.pause();
  }
  
  // 获取覆盖层和退出按钮
  const overlay = document.getElementById('study-mode-overlay');
  const exitButton = document.getElementById('exit-study-mode');
  const subtitleContainer = document.getElementById('extension-subtitle-container');
  
  // 添加淡出动画类
  if (overlay) overlay.classList.add('exiting');
  if (exitButton) exitButton.classList.add('exiting');
  if (subtitleContainer) subtitleContainer.classList.add('exiting');
  
  // 播放淡出动画
  playerContainer.classList.add('exiting');
  
  // 清理字幕文件
  if (subtitleManager && subtitleManager.subtitleFileId) {
    console.log('退出学习模式时清理字幕文件');
    window.StudyModeUtils.cleanupSubtitleFiles(subtitleManager.subtitleFileId);
  }
  
  // 延迟操作以等待动画完成
  setTimeout(() => {
    // 移除学习模式标记类
    playerContainer.classList.remove('in-study-mode');
    playerContainer.classList.remove('exiting');
    
    // 还原原始样式
    Object.keys(originalState.styles).forEach(prop => {
      playerContainer.style[prop] = originalState.styles[prop];
    });
    
    // 移回原始位置
    if (originalState.parent) {
      if (originalState.nextSibling) {
        originalState.parent.insertBefore(playerContainer, originalState.nextSibling);
      } else {
        originalState.parent.appendChild(playerContainer);
      }
    }
    
    // 隐藏覆盖层
    if (overlay) {
      overlay.style.display = 'none';
      overlay.classList.remove('exiting');
    }
    
    // 隐藏退出按钮
    if (exitButton) {
      exitButton.style.display = 'none';
      exitButton.classList.remove('exiting');
    }
    
    // 恢复页面滚动状态
    document.body.style.overflow = originalState.bodyOverflow || '';
    document.documentElement.style.overflow = originalState.htmlOverflow || '';
    
    // 恢复隐藏的页面内容
    window.StudyModeUtils.showPageContent();
    
    // 触发窗口resize事件，让YouTube播放器重新计算布局
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);

    // 清理各模块
    cleanupSubtitleManager();
    window.StudyModeChat.cleanupChatArea();
    window.StudyModeControls.cleanupProgressAndControls();
    cleanupDanmakuSystem();
    cleanupStoryboardPreview();
    
    // 清理resize监听器
    if (studyModeResizeHandler) {
      window.removeEventListener('resize', studyModeResizeHandler);
      studyModeResizeHandler = null;
    }
    
    // 清理视频切换监听器
    cleanupVideoChangeListener();

    // 清理双击全屏监听器
    cleanupPlayerDoubleClickFullscreen();

    // 恢复视频状态
    if (videoElement && isPlaying) {
      // 尝试自动播放
      videoElement.play().catch(e => console.log('自动播放失败:', e));
    }
    if (videoElement) {
      videoElement.currentTime = currentTime;
    }
    
    // 重置状态
    window.studyModeActive = false;
    studyModeActive = false;

    window.StudyModeUtils.showEnhancedNotification('已退出学习模式', '您可以继续正常浏览YouTube');
  }, 500); // 与CSS动画时长匹配
}

/**
 * 字幕管理器初始化
 */

// 初始化字幕管理器
async function initializeSubtitleManager(playerContainer) {
  try {
    console.log('初始化字幕管理器');
    
    // 检查字幕管理器类是否可用
    if (typeof ExtensionSubtitleManager === 'undefined') {
      throw new Error('ExtensionSubtitleManager 类未定义，请检查 subtitle-manager.js 是否正确加载');
    }
    
    // 创建字幕管理器实例
    subtitleManager = new ExtensionSubtitleManager(playerContainer);
    
    // 创建字幕UI
    subtitleManager.createSubtitleUI();
    
    // 获取字幕容器添加动画类
    const subtitleContainer = document.getElementById('extension-subtitle-container');
    if (subtitleContainer) {
      subtitleContainer.classList.add('entering');
      
      // 延迟移除动画类以触发过渡效果
      setTimeout(() => {
        subtitleContainer.classList.remove('entering');
        
        // 在字幕UI加载动画完成后显示欢迎通知，确保通知在最上层
        setTimeout(() => {
          window.StudyModeUtils.showEnhancedNotification('进入学习模式', '现在您可以专注于学习视频内容');
        }, 500);
      }, 50);
    }
    
    // 初始化词组tooltip功能
    window.StudyModeTooltip.initializeWordTooltip();
    
    // 获取当前视频URL并加载字幕
    const currentUrl = window.location.href;
    if (currentUrl && currentUrl.includes('youtube.com/watch')) {
      console.log('开始加载当前视频的字幕:', currentUrl);
      
      // 确保使用正确的方法名称和参数
      await subtitleManager.loadSubtitles(currentUrl);
    } else {
      console.warn('无法获取有效的YouTube视频URL:', currentUrl);
    }
    
  } catch (error) {
    console.error('字幕管理器初始化失败:', error);
    window.StudyModeUtils.showEnhancedNotification('字幕功能初始化失败', error.message);
  }
}

// 清理字幕管理器
function cleanupSubtitleManager() {
  if (subtitleManager) {
    console.log('清理字幕管理器');
    subtitleManager.cleanup();
    subtitleManager = null;
  }
  
  // 清理tooltip功能
  window.StudyModeTooltip.cleanupTooltip();
}

/**
 * 弹幕系统初始化
 */

// 初始化弹幕系统
async function initializeDanmakuSystem(playerContainer) {
  console.log('初始化弹幕系统');
  
  try {
    // 加载弹幕模块
    await loadDanmakuModule();
    
    // 等待弹幕模块加载完成
    await waitForDanmakuModule();
    
    // 初始化弹幕系统
    window.StudyModeDanmaku.init(playerContainer);
    
    console.log('弹幕系统初始化完成');
    return true;
  } catch (error) {
    console.error('弹幕系统初始化失败:', error);
    window.StudyModeUtils.showEnhancedNotification('弹幕功能初始化失败', error.message);
    return false;
  }
}

// 加载弹幕模块
function loadDanmakuModule() {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.StudyModeDanmaku) {
      resolve();
      return;
    }
    
    // 动态加载弹幕模块JavaScript文件
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('modules/study-mode/study-mode-danmaku.js');
    script.onload = () => {
      console.log('弹幕模块JavaScript已加载');
      resolve();
    };
    script.onerror = () => {
      console.error('弹幕模块JavaScript加载失败');
      reject(new Error('弹幕模块加载失败'));
    };
    
    document.head.appendChild(script);
  });
}

// 等待弹幕模块加载完成
function waitForDanmakuModule() {
  return new Promise((resolve, reject) => {
    let retries = 0;
    const maxRetries = 50; // 最多等待5秒
    
    const checkModule = () => {
      if (window.StudyModeDanmaku) {
        resolve();
      } else if (retries < maxRetries) {
        retries++;
        setTimeout(checkModule, 100);
      } else {
        reject(new Error('弹幕模块加载超时'));
      }
    };
    
    checkModule();
  });
}

// 清理弹幕系统
function cleanupDanmakuSystem() {
  if (window.StudyModeDanmaku) {
    console.log('清理弹幕系统');
    window.StudyModeDanmaku.cleanup();
  }
}

/**
 * 缩略图预览初始化
 */

// 初始化缩略图预览功能
async function initializeStoryboardPreview() {
  console.log('[StudyMode] 初始化缩略图预览功能');

  try {
    // 加载storyboard模块
    await loadStoryboardModule();

    // 等待storyboard模块加载完成
    await waitForStoryboardModule();

    console.log('[StudyMode] Storyboard模块已加载');

    // 检查是否已有配置（退出学习模式时保留的配置）
    if (window.StudyModeStoryboard && window.StudyModeStoryboard.hasConfig()) {
      console.log('[StudyMode] ✅ 发现现有配置，重新创建UI元素');
      // 如果有配置，重新创建UI元素
      const manager = window.StudyModeStoryboard._manager;
      if (manager && manager.config) {
        manager.createPreviewElements();
        console.log('[StudyMode] ✅ UI元素重新创建完成');
        return true;
      }
    }

    // 如果没有配置，尝试使用现有数据初始化
    setTimeout(async () => {
      if (window.StudyModeStoryboard && !window.StudyModeStoryboard.hasConfig()) {
        console.log('[StudyMode] 尝试使用现有数据初始化storyboard...');
        const initialized = await window.StudyModeStoryboard.initialize();
        if (initialized) {
          console.log('[StudyMode] ✅ Storyboard初始化成功');
        } else {
          console.log('[StudyMode] ⏳ 等待hook事件提供数据...');
        }
      }
    }, 2000); // 延迟2秒

    return true;
  } catch (error) {
    console.error('[StudyMode] 缩略图预览功能初始化失败:', error);
    return false;
  }
}

// 加载storyboard模块
function loadStoryboardModule() {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.StudyModeStoryboard) {
      resolve();
      return;
    }
    
    // 动态加载storyboard模块JavaScript文件
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('modules/study-mode/study-mode-storyboard.js');
    script.onload = () => {
      console.log('Storyboard模块JavaScript已加载');
      resolve();
    };
    script.onerror = () => {
      console.error('Storyboard模块JavaScript加载失败');
      reject(new Error('Storyboard模块加载失败'));
    };
    
    document.head.appendChild(script);
  });
}

// 等待storyboard模块加载完成
function waitForStoryboardModule() {
  return new Promise((resolve, reject) => {
    let retries = 0;
    const maxRetries = 50; // 最多等待5秒
    
    const checkModule = () => {
      if (window.StudyModeStoryboard) {
        resolve();
      } else if (retries < maxRetries) {
        retries++;
        setTimeout(checkModule, 100);
      } else {
        reject(new Error('Storyboard模块加载超时'));
      }
    };
    
    checkModule();
  });
}

// 清理storyboard功能
function cleanupStoryboardPreview() {
  if (window.StudyModeStoryboard) {
    console.log('清理缩略图预览功能UI元素，保留配置和缓存');
    // 只清理UI元素，保留配置和缓存，这样重新进入学习模式时缩略图仍然可用
    window.StudyModeStoryboard.cleanupUI();
  }
}

/**
 * 视频切换监听
 */

// 设置视频切换监听
function setupVideoChangeListener() {
  // 记录当前视频ID
  currentVideoId = window.StudyModeUtils.extractVideoId(window.location.href);
  console.log('当前视频ID:', currentVideoId);
  
  // 监听URL变化（YouTube使用pushState进行页面切换）
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;
  
  studyModeUrlChangeHandler = function() {
    const newVideoId = window.StudyModeUtils.extractVideoId(window.location.href);
    console.log(`[视频切换监听] 当前视频ID: ${currentVideoId}, 新视频ID: ${newVideoId}`);
    
    if (newVideoId && newVideoId !== currentVideoId) {
      console.log(`[视频切换监听] 检测到视频切换: ${currentVideoId} -> ${newVideoId}`);
      
      // 清理旧视频的字幕文件
      if (subtitleManager && subtitleManager.subtitleFileId) {
        console.log('[视频切换监听] 视频切换时清理旧字幕文件:', subtitleManager.subtitleFileId);
        window.StudyModeUtils.cleanupSubtitleFiles(subtitleManager.subtitleFileId);
      }
      
      // 重置字幕管理器状态，防止新旧字幕重叠
      if (subtitleManager && typeof subtitleManager.clearSubtitleState === 'function') {
        console.log('[视频切换监听] 清空字幕管理器状态');
        subtitleManager.clearSubtitleState();
      } else if (subtitleManager) {
        // 如果clearSubtitleState方法不存在，手动清理
        console.log('[视频切换监听] 手动清理字幕管理器状态');
        if (subtitleManager.subtitleInterval) {
          clearInterval(subtitleManager.subtitleInterval);
          subtitleManager.subtitleInterval = null;
        }
        subtitleManager.jaSubtitles = [];
        subtitleManager.zhSubtitles = [];
        subtitleManager.lastSubtitleText = { ja: '', zh: '' };
        subtitleManager.areSubtitlePathsFetched = false;
        subtitleManager.subtitleFileId = null;
        subtitleManager.subtitleJaPath = '';
        subtitleManager.subtitleZhPath = '';
        
        // 清空字幕显示区域
        const jaSubtitleEl = document.getElementById('extension-subtitle-ja');
        const zhSubtitleEl = document.getElementById('extension-subtitle-zh');
        if (jaSubtitleEl) jaSubtitleEl.innerHTML = '';
        if (zhSubtitleEl) zhSubtitleEl.textContent = '';
      }
      
      currentVideoId = newVideoId;
      
      // 重置聊天区域
      window.StudyModeChat.resetChatForNewVideo();
      
      // 延迟加载新视频的字幕，确保页面已完全更新
      setTimeout(() => {
        if (subtitleManager && window.location.href.includes('youtube.com/watch')) {
          console.log('[视频切换监听] 开始加载新视频的字幕:', window.location.href);
          subtitleManager.loadSubtitles(window.location.href);
        }
        
        // 清理storyboard缓存，等待新视频的hook事件
        if (window.StudyModeStoryboard) {
          console.log('[视频切换监听] 清理storyboard缓存，等待新视频数据');
          window.StudyModeStoryboard.clearCache();
          window.StudyModeStoryboard.destroy(); // 清理旧配置

          // hook会自动处理新视频的数据，无需手动重新初始化
          console.log('[视频切换监听] 等待hook事件提供新视频的storyboard数据');
        }
      }, 1000); // 延迟1秒确保YouTube页面完全加载
    }
  };
  
  // 重写pushState和replaceState
  history.pushState = function(...args) {
    originalPushState.apply(history, args);
    setTimeout(studyModeUrlChangeHandler, 100); // 延迟检查，确保URL已更新
  };
  
  history.replaceState = function(...args) {
    originalReplaceState.apply(history, args);
    setTimeout(studyModeUrlChangeHandler, 100);
  };
  
  // 监听popstate事件（浏览器前进后退）
  window.addEventListener('popstate', studyModeUrlChangeHandler);
  
  // 保存原始函数以便清理时恢复
  window.studyModeOriginalPushState = originalPushState;
  window.studyModeOriginalReplaceState = originalReplaceState;
}

// 清理视频切换监听器
function cleanupVideoChangeListener() {
  // 恢复原始的pushState和replaceState
  if (window.studyModeOriginalPushState) {
    history.pushState = window.studyModeOriginalPushState;
    window.studyModeOriginalPushState = null;
  }
  
  if (window.studyModeOriginalReplaceState) {
    history.replaceState = window.studyModeOriginalReplaceState;
    window.studyModeOriginalReplaceState = null;
  }
  
  // 移除popstate监听器
  if (studyModeUrlChangeHandler) {
    window.removeEventListener('popstate', studyModeUrlChangeHandler);
    studyModeUrlChangeHandler = null;
  }
  
  console.log('视频切换监听器已清理');
}

/**
 * 双击播放器切换全屏功能
 */

// 设置双击播放器切换全屏
function setupPlayerDoubleClickFullscreen(playerContainer) {
  if (!playerContainer) return;

  // 创建双击事件处理函数
  playerDoubleClickHandler = function(event) {
    // 确保点击的是播放器区域，而不是控制按钮
    const target = event.target;
    const isControlElement = target.closest('.ytp-chrome-bottom') ||
                           target.closest('.ytp-chrome-top') ||
                           target.closest('.study-mode-progress-container') ||
                           target.closest('.subtitle-control-button') ||
                           target.closest('.control-button');

    if (!isControlElement) {
      // 调用全屏切换功能
      if (window.StudyModeControls && window.StudyModeControls.toggleFullscreen) {
        window.StudyModeControls.toggleFullscreen();
      }
    }
  };

  // 添加双击事件监听器
  playerContainer.addEventListener('dblclick', playerDoubleClickHandler);
  console.log('双击播放器切换全屏功能已启用');
}

// 清理双击播放器切换全屏
function cleanupPlayerDoubleClickFullscreen() {
  if (playerDoubleClickHandler) {
    const playerContainer = document.getElementById('movie_player') ||
                           document.querySelector('.html5-video-player');
    if (playerContainer) {
      playerContainer.removeEventListener('dblclick', playerDoubleClickHandler);
    }
    playerDoubleClickHandler = null;
    console.log('双击播放器切换全屏功能已清理');
  }
}

/**
 * 消息监听和页面初始化
 */

// 监听来自插件的消息
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  if (message.action === 'toggleStudyMode') {
    console.log('收到切换学习模式的消息，准备进入学习模式');
    // 获取当前视频信息用于日志记录
    const videoId = new URLSearchParams(window.location.search).get('v') || '未知';
    const videoTitle = document.querySelector('yt-formatted-string.ytd-watch-metadata')?.textContent || '未知标题';
    console.log('当前视频信息:', {videoId, videoTitle});
    
    // 直接调用toggleStudyMode，它会自己处理状态检查和同步
    toggleStudyMode()
      .then(() => {
        sendResponse({success: true});
      })
      .catch(error => {
        console.error('切换学习模式时出错:', error);
        sendResponse({success: false, error: error.message});
      });
    
    return true; // 保持消息通道开放以进行异步响应
  } else if (message.action === 'toggleDarkMode') {
    // 处理主题切换
    window.StudyModeUtils.applyDarkMode(message.isDarkMode);
  } else if (message.action === 'checkSession') {
    // 异步执行会话检查，不阻塞响应
    window.StudyModeSession.checkStudyModeAndSession().then(() => {
      // 会话检查完成
    }).catch(err => {
      console.error('会话检查错误:', err);
    });
    
    // 立即响应，不等待检查结果
    sendResponse({success: true});
  } else if (message.action === 'sessionExpired') {
    // 处理会话过期事件
    console.log('收到会话过期通知，退出学习模式');
    
    // 如果在学习模式中，退出
    if (studyModeActive) {
      studyModeActive = false;
      disableStudyMode();
      // 显示通知
      window.StudyModeUtils.showNotification('会话已过期', '请重新登录后继续使用学习模式', 'error');
      
      // 通知父页面刷新登录状态
      try {
        window.parent.postMessage({action: 'sessionExpired'}, '*');
      } catch (e) {
        console.error('无法通知父页面会话过期:', e);
      }
    }
    
    sendResponse({success: true});
  }
  
  return true; // 保持消息通道开放以进行异步响应
});

// 页面加载完成后的初始化
if (document.readyState === 'complete') {
  initializePage();
} else {
  window.addEventListener('load', initializePage);
}

function initializePage() {
  window.StudyModeSession.setupVideoListeners();
  window.StudyModeUtils.ensureScrollingWorks();
  
  // 检查并应用保存的主题设置
  chrome.storage.sync.get(['darkMode'], function(result) {
    if (result.darkMode) {
      window.StudyModeUtils.applyDarkMode(true);
    }
  });
  
  // 添加页面卸载事件监听
  window.StudyModeSession.setupPageUnloadListener();
}

// 定期检查滚动状态
setInterval(window.StudyModeUtils.ensureScrollingWorks, 5000);

// Export the toggle function for use in other scripts
window.toggleStudyMode = toggleStudyMode;

// 导出核心模块功能，供外部调用
window.StudyModeCore = {
  // 主要控制函数
  enterStudyMode,
  exitStudyMode,
  toggleStudyMode,
  
  // 状态访问
  getStudyModeActive: () => studyModeActive,
  getSubtitleManager: () => subtitleManager,
  
  // 内部函数（供测试使用）
  enableStudyMode,
  disableStudyMode,
  initializeSubtitleManager,
  cleanupSubtitleManager,
  setupVideoChangeListener,
  cleanupVideoChangeListener,
  setupPlayerDoubleClickFullscreen,
  cleanupPlayerDoubleClickFullscreen
}; 