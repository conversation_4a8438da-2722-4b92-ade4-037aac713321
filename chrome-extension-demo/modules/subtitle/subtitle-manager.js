// Extension Subtitle Manager - 主入口文件，组合所有模块
class ExtensionSubtitleManager {
    // 静态属性，用于记录上次会话验证时间
    static lastSessionCheckTime = 0;
    
    constructor(playerContainer) {
        this.playerContainer = playerContainer;
        
        // 初始化核心模块
        this.core = new SubtitleCore(playerContainer);
        
        // 初始化UI模块
        this.ui = new SubtitleUI(this.core);
        
        // 初始化TTS模块
        this.tts = new SubtitleTTS(this.core);
        
        // 初始化转录模块
        this.transcription = new SubtitleTranscription(this.core);
        
        // 初始化分析模块
        this.analysis = new SubtitleAnalysis(this.core);
        
        // 将UI引用传递给core，以便core可以显示通知
        this.core.ui = this.ui;
        
        // 初始化TTS功能
        this.tts.init();
        
        console.log('字幕管理器初始化完成');
    }

    // 为了保持向后兼容性，代理核心模块的属性
    get jaSubtitles() { return this.core.jaSubtitles; }
    set jaSubtitles(value) { this.core.jaSubtitles = value; }
    
    get zhSubtitles() { return this.core.zhSubtitles; }
    set zhSubtitles(value) { this.core.zhSubtitles = value; }
    
    get subtitleInterval() { return this.core.subtitleInterval; }
    set subtitleInterval(value) { this.core.subtitleInterval = value; }
    
    get lastSubtitleText() { return this.core.lastSubtitleText; }
    set lastSubtitleText(value) { this.core.lastSubtitleText = value; }
    
    get subtitleContainer() { return this.ui.subtitleContainer; }
    set subtitleContainer(value) { this.ui.subtitleContainer = value; }
    
    get isLoading() { return this.core.isLoading; }
    set isLoading(value) { this.core.isLoading = value; }
    
    get subtitleFileId() { return this.core.subtitleFileId; }
    set subtitleFileId(value) { this.core.subtitleFileId = value; }
    
    get subtitleJaPath() { return this.core.subtitleJaPath; }
    set subtitleJaPath(value) { this.core.subtitleJaPath = value; }
    
    get subtitleZhPath() { return this.core.subtitleZhPath; }
    set subtitleZhPath(value) { this.core.subtitleZhPath = value; }
    
    get areSubtitlePathsFetched() { return this.core.areSubtitlePathsFetched; }
    set areSubtitlePathsFetched(value) { this.core.areSubtitlePathsFetched = value; }
    
    // TTS相关属性代理
    get isTTSEnabled() { return this.tts.isTTSEnabled; }
    set isTTSEnabled(value) { this.tts.isTTSEnabled = value; }
    
    get isTranscribing() { return this.transcription.isTranscribing; }
    set isTranscribing(value) { this.transcription.isTranscribing = value; }
    
    get transcriptionLanguage() { return this.transcription.transcriptionLanguage; }
    set transcriptionLanguage(value) { this.transcription.transcriptionLanguage = value; }

    // 代理核心方法
    createSubtitleUI() {
        return this.ui.createSubtitleUI();
    }
    
    showSubtitleLoadingAnimation() {
        return this.core.showSubtitleLoadingAnimation();
    }
    
    updateSubtitleLoadingHint(message, color) {
        return this.core.updateSubtitleLoadingHint(message, color);
    }
    
    async fetchSubtitlesFromServer() {
        return await this.core.fetchSubtitlesFromServer();
    }
    
    async loadSubtitles(videoUrl) {
        return await this.core.loadSubtitles(videoUrl);
    }
    
    async onPlayerReady() {
        return await this.core.onPlayerReady();
    }
    
    async loadSubtitleFile(subtitlePath, language) {
        return await this.core.loadSubtitleFile(subtitlePath, language);
    }
    
    async updateSubtitles() {
        return await this.core.updateSubtitles();
    }
    
    async loadSubtitleViaBackground(subtitlePath, language) {
        return await this.core.loadSubtitleViaBackground(subtitlePath, language);
    }
    
    cleanupSubtitles() {
        return this.core.cleanupSubtitles();
    }

    // 代理UI方法
    setupKeyboardListeners() {
        return this.ui.setupKeyboardListeners();
    }
    
    createSummaryModal() {
        return this.ui.createSummaryModal();
    }
    
    showNotification(message, type = 'info') {
        return this.ui.showNotification(message, type);
    }

    // 代理TTS方法
    toggleTTS(enabled) {
        return this.tts.toggleTTS(enabled);
    }
    
    resetTTS() {
        return this.tts.resetTTS();
    }
    
    addToTTSQueue(text) {
        return this.tts.addToTTSQueue(text);
    }
    
    async processTTSQueue() {
        return await this.tts.processTTSQueue();
    }
    
    async generateAndPlayTTS(text) {
        return await this.tts.generateAndPlayTTS(text);
    }
    
    async playAudioFromBlob(audioBlob) {
        return await this.tts.playAudioFromBlob(audioBlob);
    }
    
    preloadNextSubtitle(currentTime) {
        return this.tts.preloadNextSubtitle(currentTime);
    }
    
    stopSpeaking() {
        return this.tts.stopSpeaking();
    }

    // 代理转录方法
    async startTranscription() {
        return await this.transcription.startTranscription();
    }
    
    pollTranscriptionProgress(taskId) {
        return this.transcription.pollTranscriptionProgress(taskId);
    }
    
    updateTranscriptionProgress(progressData) {
        return this.transcription.updateTranscriptionProgress(progressData);
    }
    
    loadTranscribedSubtitles(response) {
        return this.transcription.loadTranscribedSubtitles(response);
    }
    
    handleTranscriptionError(errorMessage) {
        return this.transcription.handleTranscriptionError(errorMessage);
    }

    // 代理分析方法
    async analyzeCurrentSubtitle() {
        return await this.analysis.analyzeCurrentSubtitle();
    }
    
    async showAnalysisPanel(jaText, zhText) {
        return await this.analysis.showAnalysisPanel(jaText, zhText);
    }
    
    generateVideoSummary() {
        return this.analysis.generateVideoSummary();
    }
    
    generateMindMap() {
        return this.analysis.generateMindMap();
    }
    
    jumpToPreviousSubtitle() {
        return this.analysis.jumpToPreviousSubtitle();
    }
    
    jumpToNextSubtitle() {
        return this.analysis.jumpToNextSubtitle();
    }

    // 工具方法代理
    findCurrentSubtitleIndex(currentTime) {
        return SubtitleUtils.findCurrentSubtitleIndex(this.jaSubtitles, currentTime);
    }
    
    formatTime(seconds) {
        return SubtitleUtils.formatTime(seconds);
    }
    
    parseVTT(vttContent) {
        return SubtitleUtils.parseVTT(vttContent);
    }
    
    parseVttTime(timeStr) {
        return SubtitleUtils.parseVttTime(timeStr);
    }
    
    base64ToBlob(base64Data) {
        return SubtitleUtils.base64ToBlob(base64Data);
    }

    // 清理所有模块
    cleanup() {
        console.log('开始清理字幕管理器');
        
        // 清理各个模块
        this.tts.cleanup();
        this.transcription.cleanup();
        this.analysis.cleanup();
        this.ui.cleanup();
        this.core.cleanup();
        
        console.log('字幕管理器清理完成');
    }
}

// 导出供study-mode.js使用
window.ExtensionSubtitleManager = ExtensionSubtitleManager;