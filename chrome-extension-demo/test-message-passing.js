// 测试消息传递机制的脚本
// 在浏览器控制台中运行此脚本来测试content script与注入脚本的通信

console.log('🧪 开始测试消息传递机制...');

// 消息传递辅助函数
function sendMessageToSubtitleListener(action, data = null, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const requestId = Date.now() + Math.random();
    
    console.log(`📤 发送消息: ${action}, requestId: ${requestId}`);
    
    const timeoutId = setTimeout(() => {
      window.removeEventListener('message', responseHandler);
      reject(new Error(`通信超时: ${action}`));
    }, timeout);
    
    const responseHandler = (event) => {
      if (event.source !== window) return;
      
      const message = event.data;
      if (!message || 
          message.type !== 'SUBTITLE_LISTENER_RESPONSE' || 
          message.requestId !== requestId) {
        return;
      }
      
      console.log(`📥 收到响应: ${action}, success: ${message.success}`);
      
      clearTimeout(timeoutId);
      window.removeEventListener('message', responseHandler);
      
      if (message.success) {
        resolve(message.data);
      } else {
        reject(new Error(message.error || '操作失败'));
      }
    };
    
    window.addEventListener('message', responseHandler);
    
    window.postMessage({
      type: 'SUBTITLE_LISTENER_REQUEST',
      requestId: requestId,
      action: action,
      data: data
    }, '*');
  });
}

// 测试各种消息传递功能
async function runTests() {
  console.log('🔍 开始测试各种功能...');
  
  try {
    // 测试1: 检查状态
    console.log('\n1️⃣ 测试状态检查...');
    const status = await sendMessageToSubtitleListener('checkStatus');
    console.log('✅ 状态检查成功:', status);
    
    // 测试2: 获取已捕获的URL
    console.log('\n2️⃣ 测试获取已捕获的URL...');
    const capturedUrls = await sendMessageToSubtitleListener('getCapturedUrls');
    console.log('✅ 已捕获的URL:', capturedUrls);
    
    // 测试3: 获取双语URL
    console.log('\n3️⃣ 测试获取双语URL...');
    const bilingualUrls = await sendMessageToSubtitleListener('getBilingualUrls');
    console.log('✅ 双语URL:', bilingualUrls);
    
    // 测试4: 如果双语URL可用，测试实际获取字幕
    if (bilingualUrls.target && bilingualUrls.chinese) {
      console.log('\n4️⃣ 测试实际获取字幕数据...');
      await testFetchSubtitleData(bilingualUrls);
    } else {
      console.log('\n⚠️ 双语URL未准备就绪，跳过字幕数据测试');
      
      // 尝试点击字幕按钮
      console.log('\n🔘 尝试点击字幕按钮...');
      await sendMessageToSubtitleListener('clickSubtitleButton');
      console.log('✅ 字幕按钮点击完成');
    }
    
    console.log('\n🎉 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 测试实际获取字幕数据
async function testFetchSubtitleData(bilingualUrls) {
  try {
    console.log('📥 获取目标语言字幕...');
    const targetResponse = await fetch(bilingualUrls.target.url);
    if (!targetResponse.ok) {
      throw new Error(`目标语言字幕请求失败: ${targetResponse.status}`);
    }
    const targetJson3 = await targetResponse.json();
    
    console.log('📥 获取中文字幕...');
    const chineseResponse = await fetch(bilingualUrls.chinese.url);
    if (!chineseResponse.ok) {
      throw new Error(`中文字幕请求失败: ${chineseResponse.status}`);
    }
    const chineseJson3 = await chineseResponse.json();
    
    console.log('✅ 字幕数据获取成功！');
    console.log('目标语言事件数量:', targetJson3.events?.length || 0);
    console.log('中文事件数量:', chineseJson3.events?.length || 0);
    
    // 如果SubtitleUtils可用，测试转换
    if (typeof SubtitleUtils !== 'undefined') {
      console.log('🔄 测试JSON3转VTT...');
      const targetVtt = SubtitleUtils.json3ToVTT(targetJson3);
      const chineseVtt = SubtitleUtils.json3ToVTT(chineseJson3);
      
      const targetSubtitles = SubtitleUtils.parseVTT(targetVtt);
      const chineseSubtitles = SubtitleUtils.parseVTT(chineseVtt);
      
      console.log('✅ 转换成功！');
      console.log(`目标语言字幕: ${targetSubtitles.length} 条`);
      console.log(`中文字幕: ${chineseSubtitles.length} 条`);
      
      // 显示前3条作为示例
      if (targetSubtitles.length > 0) {
        console.log('目标语言前3条:', targetSubtitles.slice(0, 3));
      }
      if (chineseSubtitles.length > 0) {
        console.log('中文前3条:', chineseSubtitles.slice(0, 3));
      }
    } else {
      console.log('⚠️ SubtitleUtils不可用，跳过转换测试');
    }
    
  } catch (error) {
    console.error('❌ 字幕数据获取失败:', error);
  }
}

// 检查环境
function checkEnvironment() {
  console.log('🔍 检查环境...');
  console.log('window.SubtitleListener 直接访问:', typeof window.SubtitleListener !== 'undefined');
  console.log('SubtitleUtils 可用:', typeof SubtitleUtils !== 'undefined');
  console.log('当前URL:', window.location.href);
}

// 运行测试
checkEnvironment();
runTests();

console.log('💡 提示：如果测试失败，请确保：');
console.log('1. 在YouTube视频页面运行');
console.log('2. 字幕监听器已正确注入');
console.log('3. 已有字幕请求被监听到');
