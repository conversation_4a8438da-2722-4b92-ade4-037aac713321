// 测试JSON3转换功能的脚本
// 在浏览器控制台中运行此脚本来测试JSON3到VTT的转换

console.log('🧪 开始测试JSON3转换功能...');

// 测试JSON3数据
const testJson3Data = {
  "events": [
    {
      "tStartMs": 1700,
      "dDurationMs": 2366,
      "segs": [
        { "utf8": "大家好。" }
      ]
    },
    {
      "tStartMs": 4066,
      "dDurationMs": 3000,
      "segs": [
        { "utf8": "欢迎来到" },
        { "utf8": "我的频道。" }
      ]
    },
    {
      "tStartMs": 7066,
      "dDurationMs": 2500,
      "segs": [
        { "utf8": "今天我们要学习" },
        { "utf8": "一些有趣的内容。" }
      ]
    }
  ]
};

// 测试函数
function testJson3Conversion() {
  if (typeof SubtitleUtils === 'undefined') {
    console.error('❌ SubtitleUtils 未找到，请确保字幕工具已正确加载');
    return;
  }

  console.log('✅ SubtitleUtils 已找到');

  // 测试JSON3解析
  console.log('📋 测试JSON3解析...');
  const parsedSubtitles = SubtitleUtils.parseJSON3(testJson3Data);
  console.log('解析结果:', parsedSubtitles);

  // 测试JSON3转VTT
  console.log('🔄 测试JSON3转VTT...');
  const vttContent = SubtitleUtils.json3ToVTT(testJson3Data);
  console.log('VTT内容:');
  console.log(vttContent);

  // 测试VTT解析（验证转换结果）
  console.log('🔍 验证VTT解析...');
  const vttParsed = SubtitleUtils.parseVTT(vttContent);
  console.log('VTT解析结果:', vttParsed);

  // 比较原始数据和转换后的数据
  console.log('📊 数据对比:');
  console.log('原始JSON3事件数量:', testJson3Data.events.length);
  console.log('解析后字幕数量:', parsedSubtitles.length);
  console.log('VTT解析后数量:', vttParsed.length);

  // 详细对比第一条字幕
  if (parsedSubtitles.length > 0 && vttParsed.length > 0) {
    console.log('第一条字幕对比:');
    console.log('JSON3解析:', parsedSubtitles[0]);
    console.log('VTT解析:', vttParsed[0]);
  }
}

// 测试消息传递机制
async function testMessagePassing() {
  console.log('📡 测试消息传递机制...');

  try {
    // 测试状态检查
    const status = await sendMessageToSubtitleListener('checkStatus');
    console.log('✅ 字幕监听器状态:', status);

    // 测试获取双语URL
    const bilingualUrls = await sendMessageToSubtitleListener('getBilingualUrls');
    console.log('✅ 双语URL获取成功:', bilingualUrls);

    if (bilingualUrls.target && bilingualUrls.chinese) {
      console.log('目标语言URL:', bilingualUrls.target.url);
      console.log('中文URL:', bilingualUrls.chinese.url);

      // 测试实际获取字幕数据
      testFetchSubtitleData(bilingualUrls);
    } else {
      console.warn('⚠️ 双语URL未准备就绪');
    }

  } catch (error) {
    console.error('❌ 消息传递测试失败:', error);
  }
}

// 消息传递辅助函数
function sendMessageToSubtitleListener(action, data = null, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const requestId = Date.now() + Math.random();

    const timeoutId = setTimeout(() => {
      window.removeEventListener('message', responseHandler);
      reject(new Error(`通信超时: ${action}`));
    }, timeout);

    const responseHandler = (event) => {
      if (event.source !== window) return;

      const message = event.data;
      if (!message ||
          message.type !== 'SUBTITLE_LISTENER_RESPONSE' ||
          message.requestId !== requestId) {
        return;
      }

      clearTimeout(timeoutId);
      window.removeEventListener('message', responseHandler);

      if (message.success) {
        resolve(message.data);
      } else {
        reject(new Error(message.error || '操作失败'));
      }
    };

    window.addEventListener('message', responseHandler);

    window.postMessage({
      type: 'SUBTITLE_LISTENER_REQUEST',
      requestId: requestId,
      action: action,
      data: data
    }, '*');
  });
}

// 测试双语URL获取（兼容旧方式）
function testBilingualUrls() {
  console.log('🌐 测试双语URL获取...');

  if (typeof window.SubtitleListener !== 'undefined') {
    console.log('✅ 直接访问方式可用');
    const bilingualUrls = window.SubtitleListener.getBilingualUrls();
    console.log('双语URL:', bilingualUrls);
  } else {
    console.log('⚠️ 直接访问不可用，尝试消息传递方式');
    testMessagePassing();
  }
}

// 测试实际获取字幕数据
async function testFetchSubtitleData(bilingualUrls) {
  console.log('📥 测试实际获取字幕数据...');
  
  try {
    // 获取目标语言字幕
    console.log('获取目标语言字幕...');
    const targetResponse = await fetch(bilingualUrls.target.url);
    const targetJson3 = await targetResponse.json();
    console.log('目标语言JSON3数据:', targetJson3);
    
    // 获取中文字幕
    console.log('获取中文字幕...');
    const chineseResponse = await fetch(bilingualUrls.chinese.url);
    const chineseJson3 = await chineseResponse.json();
    console.log('中文JSON3数据:', chineseJson3);
    
    // 转换为VTT
    const targetVtt = SubtitleUtils.json3ToVTT(targetJson3);
    const chineseVtt = SubtitleUtils.json3ToVTT(chineseJson3);
    
    console.log('目标语言VTT长度:', targetVtt.length);
    console.log('中文VTT长度:', chineseVtt.length);
    
    // 解析字幕
    const targetSubtitles = SubtitleUtils.parseVTT(targetVtt);
    const chineseSubtitles = SubtitleUtils.parseVTT(chineseVtt);
    
    console.log(`✅ 转换成功！目标语言: ${targetSubtitles.length} 条，中文: ${chineseSubtitles.length} 条`);
    
    // 显示前几条字幕作为示例
    console.log('目标语言前3条字幕:', targetSubtitles.slice(0, 3));
    console.log('中文前3条字幕:', chineseSubtitles.slice(0, 3));
    
  } catch (error) {
    console.error('❌ 获取字幕数据失败:', error);
  }
}

// 运行测试
testJson3Conversion();
testBilingualUrls();

console.log('🎯 测试完成！请查看上面的输出结果。');
console.log('💡 提示：如果双语URL未准备就绪，请等待字幕监听器完成后再次运行 testBilingualUrls()。');
